import React from "react";
import { getIconComponent } from "@/lib/iconUtils";
import { UserProfile } from "@/types/user";
import { transformContactData } from "@/lib/brokenInkUtils";

interface ContactSectionProps {
  profile: UserProfile;
}

const ContactSection = ({ profile }: ContactSectionProps) => {
  const contactData = transformContactData(profile);

  // Extract email from links
  const emailLink = contactData.links.find((link) =>
    link.url.startsWith("mailto:")
  );
  const email = emailLink?.url.replace("mailto:", "") || "<EMAIL>";

  return (
    <section className="py-16 sm:py-24 bg-black" id="contact">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-6">
          {profile.user.name}
        </h2>
        <div className="text-gray-300 text-lg space-y-2">
          {emailLink && (
            <p>
              Email:{" "}
              <a
                className="hover:text-white transition-colors"
                href={emailLink.url}
              >
                {email}
              </a>
            </p>
          )}
          {contactData.phone && (
            <p>
              Phone:{" "}
              <a
                className="hover:text-white transition-colors"
                href={`tel:${contactData.phone.replace(/\D/g, "")}`}
              >
                {contactData.phone}
              </a>
            </p>
          )}
        </div>
        {contactData.socialMedia.length > 0 && (
          <div className="mt-12">
            <h3 className="text-white text-2xl font-bold mb-6">Contato</h3>
            <div className="flex justify-center gap-6">
              {contactData.socialMedia.map((social) => {
                const iconName = social.classIcon?.includes("fa-")
                  ? social.classIcon.split("fa-")[1]
                  : "globe";
                const IconComponent = getIconComponent(iconName);
                return (
                  <a
                    key={social.text}
                    className="text-gray-400 hover:text-white transition-colors"
                    href={social.url}
                    aria-label={social.text}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <IconComponent className="h-7 w-7" />
                  </a>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default ContactSection;
