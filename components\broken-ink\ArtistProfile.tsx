import React from 'react';

interface ArtistProfileProps {
  imageUrl: string;
  name: string;
  specialty: string;
}

const ArtistProfile: React.FC<ArtistProfileProps> = ({ imageUrl, name, specialty }) => {
  return (
    <div className="flex flex-col items-center gap-4">
      <div
        className="w-40 h-40 bg-center bg-cover rounded-full"
        style={{ backgroundImage: `url("${imageUrl}")` }}
        role="img" // Accessibility
        aria-label={`${name}, tattoo artist`} // Accessibility
      ></div>
      <div>
        <p className="text-white text-xl font-bold">{name}</p>
        <p className="text-gray-400">{specialty}</p>
      </div>
    </div>
  );
};

export default ArtistProfile;
