{"user": {"username": "sample-user", "name": "Sample User", "bio": "This is a sample user profile demonstrating API-based button configuration", "avatar": "https://db.avenca.cloud/images/2025/01/20/user_placeholder.png"}, "links": [{"text": "My Website", "url": "https://example.com", "classIcon": "fa fa-globe"}, {"text": "Contact Me", "url": "mailto:<EMAIL>", "classIcon": "fa fa-envelope"}], "socialMedia": [{"text": "Instagram", "url": "https://instagram.com/sample", "classIcon": "fa fa-instagram"}, {"text": "WhatsApp", "url": "https://wa.me/5511999999999", "classIcon": "fa fa-whatsapp"}], "phone": "+55 11 99999-9999", "settings": {"colors": {"background": "#1f1f1f", "linkText": "#ffffff", "primary": "#393939", "secondary": "#828282", "socialIconBackground": "#393939"}, "favicon": "", "pageDescription": "Sample user profile with custom button configuration", "pageKeywords": "sample, user, profile, buttons, api"}, "featuresSection": {"title": "My Features", "description": "Check out these amazing features with custom button configuration", "enabled": true, "buttonConfig": {"primaryButtonText": "Explore Feature", "secondaryButtonText": "Learn More", "showBadge": true}, "items": [{"id": 1, "title": "Custom Feature 1", "description": "This feature uses custom button text from API", "image": "https://images.unsplash.com/photo-1660505102581-85cffa4e6550", "primaryButton": {"icon": "fa fa-eye", "url": "https://example.com/feature1"}, "secondaryButton": {"icon": "fa fa-info-circle", "url": "https://example.com/feature1-info"}}]}, "servicesSection": {"title": "My Services", "description": "Professional services with API-defined button configuration", "enabled": true, "buttonConfig": {"primaryButtonText": "Book Service", "secondaryButtonText": "Get Quote", "showBadge": false}, "items": [{"id": 1, "title": "Custom Service 1", "description": "This service uses custom button text from API configuration", "image": "https://images.unsplash.com/photo-1660505102581-85cffa4e6550", "primaryButton": {"icon": "fa fa-calendar", "url": "https://example.com/book-service1"}, "secondaryButton": {"icon": "fa fa-phone", "url": "https://wa.me/5511999999999"}}]}, "genericSection": {"title": "Additional Content", "description": "Generic content section with default button configuration (will use fallback)", "enabled": true, "items": [{"id": 1, "title": "Generic Item 1", "description": "This item will use default button configuration since no buttonConfig is specified", "image": "https://images.unsplash.com/photo-1660505102581-85cffa4e6550", "primaryButton": {"icon": "fa fa-eye", "url": "https://example.com/generic1"}, "secondaryButton": {"icon": "fa fa-phone", "url": "https://wa.me/5511999999999"}}]}, "gallery": {"title": "Photo Gallery", "description": "My photo collection", "enabled": false, "images": []}, "reviews": {"title": "Customer Reviews", "description": "What people say about me", "enabled": false, "reviews": []}, "video": {"title": "Featured Video", "description": "Watch my latest video", "enabled": false, "youtubeUrl": ""}}