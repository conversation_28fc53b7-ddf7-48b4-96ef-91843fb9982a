@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  /* Color System */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  /* Extended Color Palette */
  --color-success: hsl(142 76% 36%);
  --color-success-foreground: hsl(355 7% 97%);
  --color-warning: hsl(38 92% 50%);
  --color-warning-foreground: hsl(48 96% 89%);
  --color-info: hsl(199 89% 48%);
  --color-info-foreground: hsl(210 40% 98%);

  /* Spacing Scale (8px grid system) */
  --space-0: 0;
  --space-1: 0.25rem;
  /* 4px */
  --space-2: 0.5rem;
  /* 8px */
  --space-3: 0.75rem;
  /* 12px */
  --space-4: 1rem;
  /* 16px */
  --space-5: 1.25rem;
  /* 20px */
  --space-6: 1.5rem;
  /* 24px */
  --space-8: 2rem;
  /* 32px */
  --space-10: 2.5rem;
  /* 40px */
  --space-12: 3rem;
  /* 48px */
  --space-16: 4rem;
  /* 64px */
  --space-20: 5rem;
  /* 80px */
  --space-24: 6rem;
  /* 96px */

  /* Border Radius Scale */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Typography Scale */
  --font-sans: var(--font-geist-sans), system-ui, sans-serif;
  --font-mono: var(--font-geist-mono), monospace;

  --text-xs: 0.75rem;
  /* 12px */
  --text-sm: 0.875rem;
  /* 14px */
  --text-base: 1rem;
  /* 16px */
  --text-lg: 1.125rem;
  /* 18px */
  --text-xl: 1.25rem;
  /* 20px */
  --text-2xl: 1.5rem;
  /* 24px */
  --text-3xl: 1.875rem;
  /* 30px */
  --text-4xl: 2.25rem;
  /* 36px */
  --text-5xl: 3rem;
  /* 48px */
  --text-6xl: 3.75rem;
  /* 60px */

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Font Weights */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Enhanced Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.2);

  /* Animation System */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.3s ease-out;
  --animate-slide-down: slideDown 0.3s ease-out;
  --animate-slide-left: slideLeft 0.3s ease-out;
  --animate-slide-right: slideRight 0.3s ease-out;
  --animate-scale-in: scaleIn 0.2s ease-out;
  --animate-scale-out: scaleOut 0.2s ease-in;
  --animate-bounce-in: bounceIn 0.6s ease-out;
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;

  /* Easing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Z-Index Scale */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    0% {
      transform: translateY(10px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    0% {
      transform: translateY(-10px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideLeft {
    0% {
      transform: translateX(10px);
      opacity: 0;
    }

    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideRight {
    0% {
      transform: translateX(-10px);
      opacity: 0;
    }

    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    0% {
      transform: scale(0.95);
      opacity: 0;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes scaleOut {
    0% {
      transform: scale(1);
      opacity: 1;
    }

    100% {
      transform: scale(0.95);
      opacity: 0;
    }
  }

  @keyframes bounceIn {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }

    50% {
      transform: scale(1.05);
    }

    70% {
      transform: scale(0.9);
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.5;
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 20 10% 10%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    min-height: max(884px, 100dvh); /* Added from original index.html */
  }
}

@layer utilities {

  /* Enhanced Shadow System */
  .shadow-xs {
    box-shadow: var(--shadow-xs);
  }

  .shadow-sm {
    box-shadow: var(--shadow-sm);
  }

  .shadow-md {
    box-shadow: var(--shadow-md);
  }

  .shadow-lg {
    box-shadow: var(--shadow-lg);
  }

  .shadow-xl {
    box-shadow: var(--shadow-xl);
  }

  .shadow-2xl {
    box-shadow: var(--shadow-2xl);
  }

  .shadow-inner {
    box-shadow: var(--shadow-inner);
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }

  /* Enhanced Easing Functions */
  .ease-linear {
    transition-timing-function: var(--ease-linear);
  }

  .ease-in {
    transition-timing-function: var(--ease-in);
  }

  .ease-out {
    transition-timing-function: var(--ease-out);
  }

  .ease-in-out {
    transition-timing-function: var(--ease-in-out);
  }

  .ease-spring {
    transition-timing-function: var(--ease-spring);
  }

  .ease-bounce {
    transition-timing-function: var(--ease-bounce);
  }

  /* Enhanced Animation System */
  .animate-fade-in {
    animation: var(--animate-fade-in);
  }

  .animate-slide-up {
    animation: var(--animate-slide-up);
  }

  .animate-slide-down {
    animation: var(--animate-slide-down);
  }

  .animate-slide-left {
    animation: var(--animate-slide-left);
  }

  .animate-slide-right {
    animation: var(--animate-slide-right);
  }

  .animate-scale-in {
    animation: var(--animate-scale-in);
  }

  .animate-scale-out {
    animation: var(--animate-scale-out);
  }

  .animate-bounce-in {
    animation: var(--animate-bounce-in);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  /* Spacing Utilities (8px grid system) */
  .space-0 {
    margin: var(--space-0);
  }

  .space-1 {
    margin: var(--space-1);
  }

  .space-2 {
    margin: var(--space-2);
  }

  .space-3 {
    margin: var(--space-3);
  }

  .space-4 {
    margin: var(--space-4);
  }

  .space-5 {
    margin: var(--space-5);
  }

  .space-6 {
    margin: var(--space-6);
  }

  .space-8 {
    margin: var(--space-8);
  }

  .space-10 {
    margin: var(--space-10);
  }

  .space-12 {
    margin: var(--space-12);
  }

  .space-16 {
    margin: var(--space-16);
  }

  .space-20 {
    margin: var(--space-20);
  }

  .space-24 {
    margin: var(--space-24);
  }

  /* Transform GPU acceleration */
  .transform-gpu {
    transform: translateZ(0);
  }

  /* Enhanced Line Clamp Utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .line-clamp-4 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
  }

  .line-clamp-5 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
  }

  .line-clamp-6 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 6;
  }

  /* Enhanced Focus Styles for Accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary;
  }

  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  /* Interactive States */
  .btn-hover-lift {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-lg;
  }

  .btn-hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary/25;
  }

  .interactive {
    @apply transition-all duration-200 ease-out hover:scale-[1.02] active:scale-[0.98];
  }

  .interactive-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }

  /* Glass Morphism Effects */
  .glass {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-light {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-dark {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Smooth Scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Carousel Utilities */
  .embla {
    overflow: hidden;
  }

  .embla__container {
    display: flex;
  }

  .embla__slide {
    flex: 0 0 auto;
    min-width: 0;
  }

  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Layout Utilities */
  .container-narrow {
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .container-wide {
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Status Colors */
  .text-success {
    color: hsl(var(--color-success));
  }

  .text-warning {
    color: hsl(var(--color-warning));
  }

  .text-info {
    color: hsl(var(--color-info));
  }

  .bg-success {
    background-color: hsl(var(--color-success));
  }

  .bg-warning {
    background-color: hsl(var(--color-warning));
  }

  .bg-info {
    background-color: hsl(var(--color-info));
  }
}