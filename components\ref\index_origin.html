<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta charset="utf-8" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link as="style"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
        onload="this.rel='stylesheet'" rel="stylesheet" />
    <title>Inkwell Studio - Where Art Meets Skin</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-black">
    <div class="relative flex size-full min-h-screen flex-col bg-black dark justify-between group/design-root overflow-x-hidden"
        style='font-family: "Space Grotesk", "Noto Sans", sans-serif;'>
        <div>
            <header class="sticky top-0 z-50 bg-black/80 backdrop-blur-sm">
                <div class="container mx-auto flex items-center justify-between p-4">
                    <a class="text-white text-xl font-bold leading-tight tracking-[-0.015em]" href="#">Inkwell
                        Studio</a>
                    <button
                        class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 bg-transparent text-white gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0 md:hidden">
                        <div class="text-white" data-icon="List" data-size="24px" data-weight="regular">
                            <svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z">
                                </path>
                            </svg>
                        </div>
                    </button>
                    <nav class="hidden md:flex items-center gap-6">
                        <a class="text-gray-300 hover:text-white transition-colors" href="#services">Services</a>
                        <a class="text-gray-300 hover:text-white transition-colors" href="#gallery">Gallery</a>
                        <a class="text-gray-300 hover:text-white transition-colors" href="#artists">Artists</a>
                        <a class="text-gray-300 hover:text-white transition-colors" href="#contact">Contact</a>
                    </nav>
                </div>
            </header>
            <main>
                <section class="relative">
                    <div class="absolute inset-0 bg-cover bg-center"
                        style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDFs1iNhGYQwhvzlt1fZ1G5g2QXqeYFaAxoOJCkoBph_6WV3X0a3r6Mk688Iv-pvUC4PQlfXKWeSX-HlFvf_Mm7Kruw-UA-r_XlsOUQgdjz8z2PAA91bln1iDf2heUJlqd8GIugAWZnC8KSLeo7sTLlVJoZwlNVcWLlQ27s8ovbB6E4xyrnTIQ1Y_MwNZcawXV9Y0-ov1eZvIbgEsi7iq3J8idq48FsO_QKBudxRGvmjJo2f8mW3QSkCBz5vx7OZ3a366H44eOWn2g");'>
                    </div>
                    <div class="absolute inset-0 bg-black/60"></div>
                    <div
                        class="relative container mx-auto flex min-h-[60vh] flex-col items-center justify-center gap-6 p-4 text-center">
                        <h1 class="text-white text-5xl font-black leading-tight tracking-[-0.033em] md:text-7xl">Inkwell
                            Studio</h1>
                        <p class="max-w-xl text-lg font-light text-gray-200 md:text-xl">Where art meets skin. Explore
                            our unique tattoo designs and skilled artists.</p>
                        <button
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-6 bg-white text-black text-base font-bold leading-normal tracking-wide transition-transform hover:scale-105">
                            <span class="truncate">Book a Consultation</span>
                        </button>
                    </div>
                </section>
                <section class="py-16 bg-black sm:py-20">
                    <div class="container mx-auto px-4 space-y-12">
                        <div class="text-center">
                            <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">Find Us On Social Media
                            </h2>
                            <p class="mt-4 text-lg leading-8 text-gray-300">Follow our journey and get inspired by our
                                latest work.</p>
                        </div>
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                            <a class="group relative flex flex-col justify-between rounded-2xl bg-gray-900/50 p-6 ring-1 ring-white/10 transition-all hover:ring-white/20"
                                href="#">
                                <div class="flex items-center gap-4">
                                    <div class="bg-gray-800 p-3 rounded-lg"><svg class="h-6 w-6 text-white" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <rect height="20" rx="5" ry="5" width="20" x="2" y="2"></rect>
                                            <path d="M16 11.37A4 4 0 1112.63 8 4 4 0 0116 11.37zm1.5-4.87h.01"></path>
                                        </svg></div>
                                    <p class="text-lg font-semibold text-white">Instagram</p>
                                </div>
                                <p class="mt-4 text-gray-400">See our portfolio and daily updates.</p>
                            </a>
                            <a class="group relative flex flex-col justify-between rounded-2xl bg-gray-900/50 p-6 ring-1 ring-white/10 transition-all hover:ring-white/20"
                                href="#">
                                <div class="flex items-center gap-4">
                                    <div class="bg-gray-800 p-3 rounded-lg"><svg class="h-6 w-6 text-white" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z">
                                            </path>
                                        </svg></div>
                                    <p class="text-lg font-semibold text-white">Facebook</p>
                                </div>
                                <p class="mt-4 text-gray-400">Join our community and events.</p>
                            </a>
                            <a class="group relative flex flex-col justify-between rounded-2xl bg-gray-900/50 p-6 ring-1 ring-white/10 transition-all hover:ring-white/20"
                                href="#">
                                <div class="flex items-center gap-4">
                                    <div class="bg-gray-800 p-3 rounded-lg"><svg class="h-6 w-6 text-white" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z">
                                            </path>
                                        </svg></div>
                                    <p class="text-lg font-semibold text-white">Twitter</p>
                                </div>
                                <p class="mt-4 text-gray-400">Quick takes and news from the studio.</p>
                            </a>
                            <a class="group relative flex flex-col justify-between rounded-2xl bg-gray-900/50 p-6 ring-1 ring-white/10 transition-all hover:ring-white/20"
                                href="#">
                                <div class="flex items-center gap-4">
                                    <div class="bg-gray-800 p-3 rounded-lg"><svg class="h-6 w-6 text-white" fill="none"
                                            height="24" stroke="currentColor" stroke-linecap="round"
                                            stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M12 2.5c-5.25 0-9.5 4.25-9.5 9.5s4.25 9.5 9.5 9.5 9.5-4.25 9.5-9.5-4.25-9.5-9.5-9.5z">
                                            </path>
                                            <path d="m9.5 8.5 2.5 2.5 2.5-2.5"></path>
                                            <path d="m14.5 15.5-2.5-2.5-2.5 2.5"></path>
                                        </svg></div>
                                    <p class="text-lg font-semibold text-white">Tattoo.com</p>
                                </div>
                                <p class="mt-4 text-gray-400">Our official industry profile.</p>
                            </a>
                        </div>
                        <div class="flex justify-center">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-8 bg-white text-black text-base font-bold leading-normal tracking-wide transition-transform hover:scale-105">
                                <span class="truncate">View All Platforms</span> </button>
                        </div>
                    </div>
                </section>
                <section class="py-16 sm:py-24" id="services">
                    <div class="container mx-auto px-4">
                        <h2 class="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
                            Our Services</h2>
                        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                            <div
                                class="flex flex-col items-center gap-4 rounded-2xl border border-gray-800 bg-gray-900/50 p-8 text-center transition-all hover:bg-gray-800/60 hover:border-gray-700">
                                <div class="rounded-full bg-gray-800 p-4 text-white">
                                    <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M189.66,66.34a8,8,0,0,1,0,11.32l-16,16a8,8,0,0,1-11.32-11.32l16-16A8,8,0,0,1,189.66,66.34ZM224,72a39.71,39.71,0,0,1-11.72,28.28l-24,24a8,8,0,0,1-4.3,2.23c-51.49,8.84-137.46,94.28-138.32,95.15h0a8,8,0,0,1-11.31-11.32h0C36,208.73,120.69,123.28,129.49,72a8,8,0,0,1,2.23-4.3l24-24A40,40,0,0,1,224,72Zm-16,0a24,24,0,0,0-41-17L144.77,77.29c-4.41,21.15-18.9,46.19-35.49,69.43,23.24-16.59,48.28-31.08,69.43-35.49L201,89A23.85,23.85,0,0,0,208,72Z">
                                        </path>
                                    </svg>
                                </div>
                                <h3 class="text-white text-xl font-bold">Custom Tattoos</h3>
                                <p class="text-gray-400">Bring your vision to life with a one-of-a-kind piece designed
                                    by our talented artists.</p>
                            </div>
                            <div
                                class="flex flex-col items-center gap-4 rounded-2xl border border-gray-800 bg-gray-900/50 p-8 text-center transition-all hover:bg-gray-800/60 hover:border-gray-700">
                                <div class="rounded-full bg-gray-800 p-4 text-white">
                                    <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M227.31,73.37,182.63,28.68a16,16,0,0,0-22.63,0L36.69,152A15.86,15.86,0,0,0,32,163.31V208a16,16,0,0,0,16,16H92.69A15.86,15.86,0,0,0,104,219.31L227.31,96a16,16,0,0,0,0-22.63ZM51.31,160,136,75.31,152.69,92,68,176.68ZM48,179.31,76.69,208H48Zm48,25.38L79.31,188,164,103.31,180.69,120Zm96-96L147.31,64l24-24L216,84.68Z">
                                        </path>
                                    </svg>
                                </div>
                                <h3 class="text-white text-xl font-bold">Flash Designs</h3>
                                <p class="text-gray-400">Choose from a curated collection of pre-drawn designs, ready to
                                    be inked.</p>
                            </div>
                            <div
                                class="flex flex-col items-center gap-4 rounded-2xl border border-gray-800 bg-gray-900/50 p-8 text-center transition-all hover:bg-gray-800/60 hover:border-gray-700">
                                <div class="rounded-full bg-gray-800 p-4 text-white">
                                    <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M232,32a8,8,0,0,0-8-8c-44.08,0-89.31,49.71-114.43,82.63A60,60,0,0,0,32,164c0,30.88-19.54,44.73-20.47,45.37A8,8,0,0,0,16,224H92a60,60,0,0,0,57.37-77.57C182.3,121.31,232,76.08,232,32ZM92,208H34.63C41.38,198.41,48,183.92,48,164a44,44,0,1,1,44,44Zm32.42-94.45q5.14-6.66,10.09-12.55A76.23,76.23,0,0,1,155,121.49q-5.9,4.94-12.55,10.09A60.54,60.54,0,0,0,124.42,113.55Zm42.7-2.68a92.57,92.57,0,0,0-22-22c31.78-34.53,55.75-45,69.9-47.91C212.17,55.12,201.65,79.09,167.12,110.87Z">
                                        </path>
                                    </svg>
                                </div>
                                <h3 class="text-white text-xl font-bold">Cover-Ups</h3>
                                <p class="text-gray-400">Expertly conceal or transform old tattoos with our creative
                                    cover-up solutions.</p>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="py-16 sm:py-24 bg-gray-900/50" id="gallery">
                    <div class="container mx-auto px-4">
                        <h2 class="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
                            Gallery</h2>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="group relative overflow-hidden rounded-2xl">
                                <div class="w-full h-80 bg-center bg-cover transition-transform duration-500 group-hover:scale-110"
                                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuA7w9N14rC0kpL-oKDNdXB2RBbbEt3bkkAiMUw90ilcNsio4NdErcvCW2QA-2GTo0gF6ll45gnFUEm0ISiGtfBJx_FPWaV_pyVQ9fjaqYtI1sbS0ZF9Nq_YQSudhoq1FFZf9ewMQSMDgJce2ILmSFKUuK-DSJdyJagpWJ46zn2WDJ1KDRBGK2CjR-jqKnRodrSmUDsppCbirxG8BzR1IL58nc1_FUpSTkfR9fHkON8tK6Zgu5alEcsQmsrPtSY-iEhJxTNkwrorqKM");'>
                                </div>
                                <div class="absolute inset-0 bg-black/40 flex items-end p-6">
                                    <p class="text-white text-lg font-medium">Intricate Sleeve</p>
                                </div>
                            </div>
                            <div class="group relative overflow-hidden rounded-2xl">
                                <div class="w-full h-80 bg-center bg-cover transition-transform duration-500 group-hover:scale-110"
                                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDmO4UzeLwRqnpPHEywfLlbQjI-XErVLbGzKV9BYT6nMOK6CbR97DAIZ9EYSP058Bgs6lkURGCEDbugnFguF2ppz-UdBULBi8KMNHU3kSfV5t2hkhP1saKh7O-VmQL0-CuXUVlIjQsZ9rIcmBSbnKmYeS38v7DK4UsF83wZqkrt01TXoWVG5SXe8ojYNaKFD9BsRgecHdbxZ3Pce8K-TpPAUfr_ohPxYU5Zur8ZaCivk0fm1LtUZk5njCAnlNb3_fiv6CoKvN_uomc");'>
                                </div>
                                <div class="absolute inset-0 bg-black/40 flex items-end p-6">
                                    <p class="text-white text-lg font-medium">Minimalist Design</p>
                                </div>
                            </div>
                            <div class="group relative overflow-hidden rounded-2xl">
                                <div class="w-full h-80 bg-center bg-cover transition-transform duration-500 group-hover:scale-110"
                                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBYp0Rf59CKZXD6rv1nbRM2U1GISnBkjBwO6VVgeaUdC3J0DPd9g0knuc_3mQ7NWvRQQ9bl_ntCoKU-3begahO2hHGqmlaED5FN6jUl0b99KNf-ceQ9Vexta2ZxYP2SJallJ5OKGRGl55j7-VYRbIVDh8JuPnOB-siSgOz_kbNeIILSCRjZga0DFYuws7fDmWXlAdsvrAIPJ4Hy4BIwYsbGdtzx2NrGdt7BC-rh3KkWV9mEKLbpSMmgJPQvk3d7giJNCQuXbNty-eQ");'>
                                </div>
                                <div class="absolute inset-0 bg-black/40 flex items-end p-6">
                                    <p class="text-white text-lg font-medium">Vibrant Color</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="py-16 sm:py-24" id="artists">
                    <div class="container mx-auto px-4">
                        <h2 class="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
                            Meet Our Artists</h2>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 text-center">
                            <div class="flex flex-col items-center gap-4">
                                <div class="w-40 h-40 bg-center bg-cover rounded-full"
                                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuD276eMEwr2iOqz4mmHWMuFUqDz7Fg5g-aXV5ePnr8XEPoE7SAcYMGhs2Q2JJHonU1zu12FN7-78c24bBLsyhVjlw2rLHtHzNlfi6yK5We_wRKlAHcFI1d0xxcrhy35KaYvxwpGGmG4TNbVTsvl4zp3x5WIKF3u407AqtvZhE_dLwT5afq8bNDCBr4i1SgvIZGRrQn_5Y_ERKS0JmWN_9S_tEJ5jr9o02sUwtDaWjDZUKmkRwSuOB-mBnbT3ONiS8JLzIpQDijmSZg");'>
                                </div>
                                <div>
                                    <p class="text-white text-xl font-bold">Ethan Carter</p>
                                    <p class="text-gray-400">Specializes in realism</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-center gap-4">
                                <div class="w-40 h-40 bg-center bg-cover rounded-full"
                                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCecqLAEOZJpFOXfof9zq8_LvxvCNJYHk9e1yCclunEljJw3uPyUEQK38XP2WcJCVFH20zzIg3Dz3jwGuDwu9xKFQC8IC2YK-3oRySz10BEc1feSuBsN4tbKR4PbM7-4Do1ZzCvK8Bz3UKe1_tuwziUifNZFJMR0tvwYraMP0rI7kRXrFNK7Gib2M77XsFq0c7iYJaMp000RzorawA_vfznbL16GDN9jOo_JxEceYN9yttyDky232CU49YHq5z-XzConRPIaNE5HyI");'>
                                </div>
                                <div>
                                    <p class="text-white text-xl font-bold">Olivia Hayes</p>
                                    <p class="text-gray-400">Known for fine lines</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-center gap-4">
                                <div class="w-40 h-40 bg-center bg-cover rounded-full"
                                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBtBwtmwgBupr4XTr_AMJGYLuN5ZIY4QPSheoJzIXV4byBVriNlVcr0A1uOB23gLFidwLetSlsJ_KBfi51795LPaLaZ-MpDwp0pUnytE71it2KVFKfLu6t49Ff__ZwAEmcmkQp-7xHUxR4f3xqBRC9tvPt8KY9RrrpFjsBZB83p-jLpxbQrmx7PzC3jmqs0HA16v5ETW5MdS7UnRC3iy6Y5wdwfYKN-J5-pxxUkDCSBgafhkvNBVtMmpGd7sYtgvJuUqrc3wm82h2E");'>
                                </div>
                                <div>
                                    <p class="text-white text-xl font-bold">Noah Bennett</p>
                                    <p class="text-gray-400">Expert in traditional</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="py-16 sm:py-24" id="location">
                    <div class="container mx-auto px-4">
                        <h2 class="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
                            Location</h2>
                        <div class="aspect-video w-full rounded-2xl overflow-hidden shadow-lg">
                            <div class="w-full h-full bg-center bg-cover"
                                style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuA0SgGy7cT5cVunCBQlV26VnU22EB2XDmnTc-dhB0yd0vypQsdRVxSCfCMLY6FCEWIcomq1cnuy2dB1DMT3eWHGBrR2jqQTlY5UF3g5f4TZxEEXaoPQPzgZkq_mQmGpuwGtxkqqG_PLttXtZJ5w9wBncccoJPJLLQkHIBn1DczMPfpcRFfyB5rCnhMbxD0O3jL5pN0-zbC9BYDaiv5_QZx4hv4hJvP_YL27tV4bEnFNbGrceKnQo-iGjFTgIohhqdkI0NrEstzw9Lc");'>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="py-16 sm:py-24 bg-gray-900/50" id="contact">
                    <div class="container mx-auto px-4 text-center">
                        <h2 class="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-6">Contact Us</h2>
                        <div class="text-gray-300 text-lg space-y-2">
                            <p>Email: <a class="hover:text-white transition-colors"
                                    href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p>Phone: <a class="hover:text-white transition-colors" href="tel:5551234567">(555)
                                    123-4567</a></p>
                            <p>Address: 123 Inkwell Street, New York, NY 10001</p>
                        </div>
                        <div class="mt-12">
                            <h3 class="text-white text-2xl font-bold mb-6">Follow Us</h3>
                            <div class="flex justify-center gap-6">
                                <a class="text-gray-400 hover:text-white transition-colors" href="#">
                                    <svg fill="currentColor" height="28px" viewBox="0 0 256 256" width="28px"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160ZM176,24H80A56.06,56.06,0,0,0,24,80v96a56.06,56.06,0,0,0,56,56h96a56.06,56.06,0,0,0,56-56V80A56.06,56.06,0,0,0,176,24Zm40,152a40,40,0,0,1-40,40H80a40,40,0,0,1-40-40V80A40,40,0,0,1,80,40h96a40,40,0,0,1,40,40ZM192,76a12,12,0,1,1-12-12A12,12,0,0,1,192,76Z">
                                        </path>
                                    </svg>
                                </a>
                                <a class="text-gray-400 hover:text-white transition-colors" href="#">
                                    <svg fill="currentColor" height="28px" viewBox="0 0 256 256" width="28px"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm8,191.63V152h24a8,8,0,0,0,0-16H136V112a16,16,0,0,1,16-16h16a8,8,0,0,0,0-16H152a32,32,0,0,0-32,32v24H96a8,8,0,0,0,0,16h24v63.63a88,88,0,1,1,16,0Z">
                                        </path>
                                    </svg>
                                </a>
                                <a class="text-gray-400 hover:text-white transition-colors" href="#">
                                    <svg fill="currentColor" height="28px" viewBox="0 0 256 256" width="28px"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z">
                                        </path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
        <footer class="sticky bottom-0 bg-gray-900/80 backdrop-blur-sm border-t border-gray-800 md:hidden">
            <div class="container mx-auto">
                <div class="flex justify-around items-center pt-2 pb-3">
                    <a class="flex flex-col items-center justify-end gap-1 text-white" href="#home">
                        <div class="flex h-8 items-center justify-center">
                            <svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z">
                                </path>
                            </svg>
                        </div>
                        <p class="text-xs font-medium leading-normal tracking-wide">Home</p>
                    </a>
                    <a class="flex flex-col items-center justify-end gap-1 text-gray-400 hover:text-white transition-colors"
                        href="#gallery">
                        <div class="flex h-8 items-center justify-center">
                            <svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,16V158.75l-26.07-26.06a16,16,0,0,0-22.63,0l-20,20-44-44a16,16,0,0,0-22.62,0L40,149.37V56ZM40,172l52-52,80,80H40Zm176,28H194.63l-36-36,20-20L216,181.38V200ZM144,100a12,12,0,1,1,12,12A12,12,0,0,1,144,100Z">
                                </path>
                            </svg>
                        </div>
                        <p class="text-xs font-medium leading-normal tracking-wide">Gallery</p>
                    </a>
                    <a class="flex flex-col items-center justify-end gap-1 text-gray-400 hover:text-white transition-colors"
                        href="#artists">
                        <div class="flex h-8 items-center justify-center">
                            <svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z">
                                </path>
                            </svg>
                        </div>
                        <p class="text-xs font-medium leading-normal tracking-wide">Artists</p>
                    </a>
                    <a class="flex flex-col items-center justify-end gap-1 text-gray-400 hover:text-white transition-colors"
                        href="#contact">
                        <div class="flex h-8 items-center justify-center">
                            <svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Zm-96-88v64a8,8,0,0,1-16,0V132.94l-4.42,2.22a8,8,0,0,1-7.16-14.32l16-8A8,8,0,0,1,112,120Zm59.16,30.45L152,176h16a8,8,0,0,1,0,16H136a8,8,0,0,1-6.4-12.8l28.78-38.37A8,8,0,1,0,145.07,132a8,8,0,1,1-13.85-8A24,24,0,0,1,176,136,23.76,23.76,0,0,1,171.16,150.45Z">
                                </path>
                            </svg>
                        </div>
                        <p class="text-xs font-medium leading-normal tracking-wide">Book</p>
                    </a>
                </div>
            </div>
        </footer>
    </div>

</body>

</html>