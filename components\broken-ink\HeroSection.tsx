import React from "react";
import { UserProfile } from "@/types/user";
import { transformHeroData } from "@/lib/brokenInkUtils";
import { Button } from "@/components/ui/button";
import { getIconComponent } from "@/lib/iconUtils";
import { Eye } from "lucide-react";

interface HeroSectionProps {
  profile: UserProfile;
}

const HeroSection = ({ profile }: HeroSectionProps) => {
  const heroData = transformHeroData(profile);
  const WhatsAppIcon = getIconComponent("whatsapp");

  return (
    <section className="relative">
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url("${heroData.backgroundImage}")`,
          backgroundPosition: "center center",
        }}
      ></div>
      {/* Aggressive gradient overlay that fades from light to very dark at bottom */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/75 to-black"></div>
      {/* Strong bottom gradient for seamless blending with black component */}
      <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-b from-transparent via-black/60 to-black"></div>
      <div className="relative container mx-auto flex min-h-[90vh] flex-col items-center justify-center md:justify-center md:gap-8 gap-10 p-4 pb-12 md:pb-16 text-center">
        <h1 className="text-white text-5xl font-black leading-tight tracking-[-0.033em] md:text-7xl mt-40">
          {heroData.title}
        </h1>
        <p className="max-w-xl text-lg font-light text-gray-200 md:text-xl mb-6 md:mb-8">
          {heroData.description}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
          <Button
            size="xl"
            animation="hover"
            className="min-w-[140px] rounded-full font-bold tracking-wide h-16"
            style={
              heroData.colors?.primary
                ? {
                    backgroundColor: heroData.colors.primary,
                    color: heroData.colors.linkText || "#ffffff",
                  }
                : {
                    backgroundColor: "#ffffff",
                    color: "#000000",
                  }
            }
            leftIcon={<WhatsAppIcon className="w-5 h-5" />}
          >
            {heroData.buttonText}
          </Button>
          <Button
            size="xl"
            variant="outline"
            animation="hover"
            className="min-w-[140px] rounded-full font-bold tracking-wide border-white/10 text-white hover:bg-white/20 bg-white/10 hover:text-white h-16"
            leftIcon={<Eye className="w-5 h-5" />}
          >
            Ver mais
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
