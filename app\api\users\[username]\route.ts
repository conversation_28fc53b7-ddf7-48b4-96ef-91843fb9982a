import { NextRequest, NextResponse } from 'next/server'
import { UserProfile, UserProfileResponse } from '@/types/user'
import { validateUsername } from '@/lib/utils'



export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> }
): Promise<NextResponse<UserProfileResponse>> {
  try {
    const { username } = await params

    // Validate username format
    if (!validateUsername(username)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid username format',
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'User not found',
      },
      { status: 404 }
    )
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
