import React from "react";

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  icon,
  title,
  description,
}) => {
  return (
    <div className="flex flex-col items-center gap-4 rounded-2xl border border-gray-800 bg-gray-900/50 p-8 text-center transition-all hover:bg-gray-800/60 hover:border-gray-700">
      <div className="rounded-full bg-gray-800 p-4 text-white">{icon}</div>
      <h3 className="text-white text-xl font-bold">{title}</h3>
      <p className="text-gray-400">{description}</p>
    </div>
  );
};

export default ServiceCard;
