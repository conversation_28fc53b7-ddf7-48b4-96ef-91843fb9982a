// ============================================================================
// TYPES INDEX - Central export for all type definitions
// ============================================================================

// Import types for runtime use in functions
import type {
  UserProfile,
  User,
  Link,
  SocialMediaLink,
  SectionItem,
  Review,
  GalleryImage,
} from './user'

import {
  ICON_CLASSES,
  DEFAULT_COLOR_SCHEMES,
  PLACEHOLDER_IMAGES,
  DEFAULT_SECTION_VALUES,
} from './constants'

// Core user and profile types
export type {
  // Main interfaces
  UserProfile,
  User,
  Link,
  SocialMediaLink,
  Button,
  ButtonConfig,

  // Section interfaces
  FeaturesSection,
  ServicesSection,
  GenericSection,
  TeamSection,
  TeamMember,
  SectionItem,

  // Gallery interfaces
  Gallery,
  GalleryImage,

  // Reviews interfaces
  ReviewsSection,
  Review,

  // Video interface
  VideoSection,

  // Location interface
  LocationData,

  // Settings interfaces
  Settings,
  ColorSettings,

  // API interfaces
  ApiResponse,
  UserProfileResponse,

  // Legacy interfaces (deprecated)
  UserLink,
  CreateUserRequest,
  UpdateUserRequest,
  CreateLinkRequest,
  UpdateLinkRequest,
  UserStats,
  ApiError,
} from './user'

// Constants and enums
export {
  ICON_CLASSES,
  DEFAULT_COLOR_SCHEMES,
  RATING_VALUES,
  SECTION_TYPES,
  BUTTON_TYPES,
  URL_PATTERNS,
  PLACEHOLDER_IMAGES,
  DEFAULT_SECTION_VALUES,
  SECTION_BUTTON_CONFIG,
} from './constants'

export type {
  IconClass,
  ColorScheme,
  RatingValue,
  SectionType,
  ButtonType,
} from './constants'

// Utility types
export type {
  // Generic utility types
  DeepPartial,
  RequiredFields,
  OptionalFields,
  ArrayElement,
  PickByType,
  OmitByType,
  ValueOf,
  NonEmptyArray,
  StringLiteral,
  NumericLiteral,
  NonNullable,
  Parameters,
  ReturnType,

  // Form and validation types
  FormField,
  ValidationResult,
  LoadingState,
  AsyncState,
  PaginationState,
  SortState,
  FilterState,

  // Event and handler types
  EventHandler,
  ChangeHandler,
  ClickHandler,
  SubmitHandler,
  AsyncEventHandler,

  // Component prop types
  BaseComponentProps,
  WithChildren,
  WithOptionalChildren,
  WithLoading,
  WithError,
  WithDisabled,
  WithVariant,
  WithSize,

  // API and data types
  ApiResponseWrapper,
  ErrorResponse,
  SuccessResponse,
  FailedResponse,
  ApiResult,
  ListMetadata,
  PaginatedResponse,
} from './utils'

// Validation types and functions
export type {
  ValidationError,
  ValidationResult as ValidationResultType,
} from './validation'

export {
  // Validation functions
  isValidUrl,
  isValidEmail,
  isValidPhone,
  isValidUsername,
  isValidHexColor,
  isValidRating,
  isValidIconClass,

  // Object validation functions
  validateUser,
  validateLink,
  validateSectionItem,
  validateReview,
  validateGalleryImage,
  validateColorSettings,
  validateButtonConfig,
  validateLocationData,
  validateUserProfile,

  // Sanitization functions
  sanitizeString,
  sanitizeUrl,
} from './validation'

// Error classes
export {
  UserNotFoundError,
  InvalidUsernameError,
} from './user'

// ============================================================================
// TYPE GUARDS
// ============================================================================

/**
 * Type guard to check if a value is a valid UserProfile
 */
export function isUserProfile(value: any): value is UserProfile {
  return (
    value &&
    typeof value === 'object' &&
    value.user &&
    typeof value.user.username === 'string' &&
    typeof value.user.name === 'string' &&
    Array.isArray(value.links) &&
    Array.isArray(value.socialMedia) &&
    value.settings &&
    value.settings.colors
  )
}

/**
 * Type guard to check if a value is a valid Link
 */
export function isLink(value: any): value is Link {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.text === 'string' &&
    typeof value.url === 'string' &&
    typeof value.classIcon === 'string'
  )
}

/**
 * Type guard to check if a value is a valid SectionItem
 */
export function isSectionItem(value: any): value is SectionItem {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'number' &&
    typeof value.title === 'string' &&
    typeof value.description === 'string' &&
    typeof value.image === 'string' &&
    value.primaryButton &&
    value.secondaryButton
  )
}

/**
 * Type guard to check if a value is a valid Review
 */
export function isReview(value: any): value is Review {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'number' &&
    typeof value.name === 'string' &&
    typeof value.comment === 'string' &&
    typeof value.rating === 'number' &&
    value.rating >= 1 &&
    value.rating <= 5
  )
}

/**
 * Type guard to check if a value is a valid GalleryImage
 */
export function isGalleryImage(value: any): value is GalleryImage {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'number' &&
    typeof value.title === 'string' &&
    typeof value.alt === 'string' &&
    typeof value.url === 'string'
  )
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Creates a default UserProfile object
 */
export function createDefaultUserProfile(username: string, name: string): UserProfile {
  return {
    user: {
      username,
      name,
      bio: '',
      avatar: PLACEHOLDER_IMAGES.USER_AVATAR,
    },
    links: [],
    socialMedia: [],
    phone: '',
    settings: {
      colors: DEFAULT_COLOR_SCHEMES.LIGHT,
      favicon: '',
      pageDescription: '',
      pageKeywords: '',
    },
    featuresSection: {
      title: '',
      description: '',
      enabled: false,
      items: [],
    },
    servicesSection: {
      title: '',
      description: '',
      enabled: false,
      items: [],
    },
    genericSection: {
      title: '',
      description: '',
      enabled: false,
      items: [],
    },
    gallery: {
      title: '',
      description: '',
      enabled: false,
      images: [],
    },
    reviews: {
      title: '',
      description: '',
      enabled: false,
      reviews: [],
    },
    video: {
      title: '',
      description: '',
      enabled: false,
      youtubeUrl: '',
    },
    location: {
      enabled: false,
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
      },
      contact: {
        phone: '',
        whatsapp: '',
      },
      hours: {
        weekdays: '',
        weekends: '',
      },
      googleMapsUrl: '',
    },
    team: {
      enabled: false,
      title: '',
      members: [],
    },
  }
}

/**
 * Creates a default Link object
 */
export function createDefaultLink(text: string, url: string, icon: string = ICON_CLASSES.PLACEHOLDER): Link {
  return {
    text,
    url,
    classIcon: icon,
  }
}

/**
 * Creates a default SectionItem object
 */
export function createDefaultSectionItem(id: number, title: string): SectionItem {
  return {
    id,
    title,
    description: '',
    image: PLACEHOLDER_IMAGES.GALLERY_IMAGE,
    primaryButton: {
      icon: ICON_CLASSES.PLACEHOLDER,
      url: DEFAULT_SECTION_VALUES.EMPTY_URL,
    },
    secondaryButton: {
      icon: ICON_CLASSES.PLACEHOLDER,
      url: DEFAULT_SECTION_VALUES.EMPTY_URL,
    },
  }
}
