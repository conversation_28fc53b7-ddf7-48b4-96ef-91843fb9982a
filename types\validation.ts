// ============================================================================
// VALIDATION SCHEMAS AND HELPERS
// ============================================================================

import type {
  UserProfile,
  User,
  Link,
  SectionItem,
  Review,
  GalleryImage,
  ColorSettings,
  ButtonConfig,
  LocationData
} from './user'

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string
  message: string
  code?: string
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validates a URL string
 */
export function isValidUrl(url: string): boolean {
  if (!url || url === '#') return true // Allow empty or placeholder URLs

  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * Validates an email address
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validates a phone number (basic validation)
 */
export function isValidPhone(phone: string): boolean {
  if (!phone) return true // Allow empty phone
  const phoneRegex = /^[\d\s\-\+\(\)]+$/
  return phoneRegex.test(phone) && phone.length >= 10
}

/**
 * Validates a username
 */
export function isValidUsername(username: string): boolean {
  if (!username) return false
  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/
  return usernameRegex.test(username)
}

/**
 * Validates a hex color
 */
export function isValidHexColor(color: string): boolean {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return hexRegex.test(color)
}

/**
 * Validates a rating value
 */
export function isValidRating(rating: number): boolean {
  return Number.isInteger(rating) && rating >= 1 && rating <= 5
}

/**
 * Validates an icon class
 */
export function isValidIconClass(iconClass: string): boolean {
  if (!iconClass) return false
  if (iconClass === '#') return true // Allow placeholder
  return iconClass.startsWith('fa fa-') || iconClass.startsWith('fas fa-') || iconClass.startsWith('fab fa-')
}

// ============================================================================
// OBJECT VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validates a User object
 */
export function validateUser(user: User): ValidationResult {
  const errors: ValidationError[] = []

  if (!user.username) {
    errors.push({ field: 'username', message: 'Username is required' })
  } else if (!isValidUsername(user.username)) {
    errors.push({ field: 'username', message: 'Invalid username format' })
  }

  if (!user.name) {
    errors.push({ field: 'name', message: 'Name is required' })
  }

  if (user.avatar && !isValidUrl(user.avatar)) {
    errors.push({ field: 'avatar', message: 'Invalid avatar URL' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates a Link object
 */
export function validateLink(link: Link): ValidationResult {
  const errors: ValidationError[] = []

  if (!link.text) {
    errors.push({ field: 'text', message: 'Link text is required' })
  }

  if (!link.url) {
    errors.push({ field: 'url', message: 'Link URL is required' })
  } else if (!isValidUrl(link.url)) {
    errors.push({ field: 'url', message: 'Invalid URL format' })
  }

  if (!link.classIcon) {
    errors.push({ field: 'classIcon', message: 'Icon class is required' })
  } else if (!isValidIconClass(link.classIcon)) {
    errors.push({ field: 'classIcon', message: 'Invalid icon class format' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates a SectionItem object
 */
export function validateSectionItem(item: SectionItem): ValidationResult {
  const errors: ValidationError[] = []

  if (!item.title) {
    errors.push({ field: 'title', message: 'Title is required' })
  }

  if (!item.description) {
    errors.push({ field: 'description', message: 'Description is required' })
  }

  if (!item.image) {
    errors.push({ field: 'image', message: 'Image URL is required' })
  } else if (!isValidUrl(item.image)) {
    errors.push({ field: 'image', message: 'Invalid image URL' })
  }

  if (item.primaryButton.url && !isValidUrl(item.primaryButton.url)) {
    errors.push({ field: 'primaryButton.url', message: 'Invalid primary button URL' })
  }

  if (item.secondaryButton.url && !isValidUrl(item.secondaryButton.url)) {
    errors.push({ field: 'secondaryButton.url', message: 'Invalid secondary button URL' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates a Review object
 */
export function validateReview(review: Review): ValidationResult {
  const errors: ValidationError[] = []

  if (!review.name) {
    errors.push({ field: 'name', message: 'Reviewer name is required' })
  }

  if (!review.comment) {
    errors.push({ field: 'comment', message: 'Review comment is required' })
  }

  if (!isValidRating(review.rating)) {
    errors.push({ field: 'rating', message: 'Rating must be between 1 and 5' })
  }

  if (review.photo && !isValidUrl(review.photo)) {
    errors.push({ field: 'photo', message: 'Invalid photo URL' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates a GalleryImage object
 */
export function validateGalleryImage(image: GalleryImage): ValidationResult {
  const errors: ValidationError[] = []

  if (!image.title) {
    errors.push({ field: 'title', message: 'Image title is required' })
  }

  if (!image.url) {
    errors.push({ field: 'url', message: 'Image URL is required' })
  } else if (!isValidUrl(image.url)) {
    errors.push({ field: 'url', message: 'Invalid image URL' })
  }

  if (!image.alt) {
    errors.push({ field: 'alt', message: 'Alt text is required for accessibility' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates ColorSettings object
 */
export function validateColorSettings(colors: ColorSettings): ValidationResult {
  const errors: ValidationError[] = []

  const colorFields: (keyof ColorSettings)[] = [
    'background',
    'linkText',
    'primary',
    'secondary',
    'socialIconBackground'
  ]

  colorFields.forEach(field => {
    const color = colors[field]
    if (!color) {
      errors.push({ field, message: `${field} color is required` })
    } else if (!isValidHexColor(color)) {
      errors.push({ field, message: `Invalid ${field} color format` })
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates ButtonConfig object
 */
export function validateButtonConfig(config: ButtonConfig): ValidationResult {
  const errors: ValidationError[] = []

  if (config.primaryButtonText !== undefined && typeof config.primaryButtonText !== 'string') {
    errors.push({ field: 'primaryButtonText', message: 'Primary button text must be a string' })
  }

  if (config.secondaryButtonText !== undefined && typeof config.secondaryButtonText !== 'string') {
    errors.push({ field: 'secondaryButtonText', message: 'Secondary button text must be a string' })
  }

  if (config.showBadge !== undefined && typeof config.showBadge !== 'boolean') {
    errors.push({ field: 'showBadge', message: 'Show badge must be a boolean' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates LocationData object
 */
export function validateLocationData(location: LocationData): ValidationResult {
  const errors: ValidationError[] = []

  if (typeof location.enabled !== 'boolean') {
    errors.push({ field: 'enabled', message: 'Enabled must be a boolean' })
  }

  // Validate address
  if (!location.address) {
    errors.push({ field: 'address', message: 'Address is required' })
  } else {
    if (!location.address.street) {
      errors.push({ field: 'address.street', message: 'Street address is required' })
    }
    if (!location.address.city) {
      errors.push({ field: 'address.city', message: 'City is required' })
    }
    if (!location.address.state) {
      errors.push({ field: 'address.state', message: 'State is required' })
    }
    if (!location.address.zipCode) {
      errors.push({ field: 'address.zipCode', message: 'ZIP code is required' })
    }
    if (!location.address.country) {
      errors.push({ field: 'address.country', message: 'Country is required' })
    }
  }

  // Validate contact
  if (!location.contact) {
    errors.push({ field: 'contact', message: 'Contact information is required' })
  } else {
    if (location.contact.phone && !isValidPhone(location.contact.phone)) {
      errors.push({ field: 'contact.phone', message: 'Invalid phone number format' })
    }
    if (location.contact.whatsapp && !isValidUrl(location.contact.whatsapp)) {
      errors.push({ field: 'contact.whatsapp', message: 'Invalid WhatsApp URL format' })
    }
  }

  // Validate hours
  if (!location.hours) {
    errors.push({ field: 'hours', message: 'Business hours are required' })
  } else {
    if (!location.hours.weekdays) {
      errors.push({ field: 'hours.weekdays', message: 'Weekday hours are required' })
    }
    if (!location.hours.weekends) {
      errors.push({ field: 'hours.weekends', message: 'Weekend hours are required' })
    }
  }

  // Validate Google Maps URL
  if (!location.googleMapsUrl) {
    errors.push({ field: 'googleMapsUrl', message: 'Google Maps URL is required' })
  } else if (!isValidUrl(location.googleMapsUrl)) {
    errors.push({ field: 'googleMapsUrl', message: 'Invalid Google Maps URL format' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates a complete UserProfile object
 */
export function validateUserProfile(profile: UserProfile): ValidationResult {
  const errors: ValidationError[] = []

  // Validate user
  const userValidation = validateUser(profile.user)
  errors.push(...userValidation.errors)

  // Validate links
  profile.links.forEach((link, index) => {
    const linkValidation = validateLink(link)
    linkValidation.errors.forEach(error => {
      errors.push({
        ...error,
        field: `links[${index}].${error.field}`
      })
    })
  })

  // Validate social media links
  profile.socialMedia.forEach((link, index) => {
    const linkValidation = validateLink(link)
    linkValidation.errors.forEach(error => {
      errors.push({
        ...error,
        field: `socialMedia[${index}].${error.field}`
      })
    })
  })

  // Validate color settings
  const colorValidation = validateColorSettings(profile.settings.colors)
  colorValidation.errors.forEach(error => {
    errors.push({
      ...error,
      field: `settings.colors.${error.field}`
    })
  })

  // Validate phone if provided
  if (profile.phone && !isValidPhone(profile.phone)) {
    errors.push({ field: 'phone', message: 'Invalid phone number format' })
  }

  // Validate button configurations for sections
  if (profile.featuresSection.buttonConfig) {
    const buttonValidation = validateButtonConfig(profile.featuresSection.buttonConfig)
    buttonValidation.errors.forEach(error => {
      errors.push({
        ...error,
        field: `featuresSection.buttonConfig.${error.field}`
      })
    })
  }

  if (profile.servicesSection.buttonConfig) {
    const buttonValidation = validateButtonConfig(profile.servicesSection.buttonConfig)
    buttonValidation.errors.forEach(error => {
      errors.push({
        ...error,
        field: `servicesSection.buttonConfig.${error.field}`
      })
    })
  }

  if (profile.genericSection.buttonConfig) {
    const buttonValidation = validateButtonConfig(profile.genericSection.buttonConfig)
    buttonValidation.errors.forEach(error => {
      errors.push({
        ...error,
        field: `genericSection.buttonConfig.${error.field}`
      })
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// ============================================================================
// SANITIZATION FUNCTIONS
// ============================================================================

/**
 * Sanitizes a string by removing potentially harmful characters
 */
export function sanitizeString(str: string): string {
  return str.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/[<>]/g, '')
    .trim()
}

/**
 * Sanitizes a URL
 */
export function sanitizeUrl(url: string): string {
  if (!url || url === '#') return url

  try {
    const urlObj = new URL(url)
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return '#'
    }
    return urlObj.toString()
  } catch {
    return '#'
  }
}
