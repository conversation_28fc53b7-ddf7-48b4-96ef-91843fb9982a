import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormField, FormLabel } from "@/components/ui/form";

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-6">
      <div className="max-w-md w-full">
        <div className="bg-card rounded-3xl shadow-strong p-8 border border-border/50 backdrop-blur-sm animate-scale-in">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg interactive">
              <span className="text-primary-foreground font-bold text-2xl">
                A
              </span>
            </div>
            <h1 className="text-3xl font-bold text-card-foreground mb-3">
              Welcome back
            </h1>
            <p className="text-muted-foreground">
              Sign in to your AvencaLink account
            </p>
          </div>

          <Form className="space-y-6">
            <FormField>
              <Input
                type="email"
                id="email"
                label="Email address"
                placeholder="<EMAIL>"
              />
            </FormField>

            <FormField>
              <Input
                type="password"
                id="password"
                label="Password"
                placeholder="••••••••"
              />
            </FormField>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary focus:ring-primary border-input rounded transition-colors"
                />
                <FormLabel htmlFor="remember-me" className="text-sm">
                  Remember me
                </FormLabel>
              </div>

              <div className="text-sm">
                <Link
                  href="/forgot-password"
                  className="text-primary hover:text-primary/80 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
            </div>

            <Button className="w-full" size="lg" animation="lift">
              Sign In
            </Button>

            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Don&apos;t have an account?{" "}
                <Link
                  href="/signup"
                  className="text-primary hover:text-primary/80 font-medium transition-colors"
                >
                  Sign up
                </Link>
              </p>
            </div>
          </Form>
        </div>

        <div className="text-center mt-8">
          <Link
            href="/"
            className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}
