import React from "react";
import ArtistProfile from "./ArtistProfile";
import { UserProfile } from "@/types/user";
import { transformArtistsData } from "@/lib/brokenInkUtils";

interface ArtistsSectionProps {
  profile: UserProfile;
}

const ArtistsSection = ({ profile }: ArtistsSectionProps) => {
  const artistsData = transformArtistsData(profile);

  if (!artistsData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24" id="artists">
      <div className="container mx-auto px-4">
        <h2 className="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
          {artistsData.title}
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 text-center">
          {artistsData.artists.map((artist) => (
            <ArtistProfile
              key={artist.name}
              imageUrl={artist.imageUrl}
              name={artist.name}
              specialty={artist.specialty}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ArtistsSection;
