"use client";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { useState } from "react";

// FAQ Item Component
function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border border-border/50 rounded-xl overflow-hidden bg-card/50 backdrop-blur-sm">
      <button
        className="w-full px-6 py-4 sm:px-8 sm:py-6 text-left hover:bg-muted/50 transition-colors duration-200 flex items-center justify-between group"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="font-semibold text-foreground text-base sm:text-lg pr-4">
          {question}
        </h3>
        <svg
          className={`w-5 h-5 text-muted-foreground transition-transform duration-200 flex-shrink-0 ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {isOpen && (
        <div className="px-6 pb-4 sm:px-8 sm:pb-6 animate-slide-down">
          <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
            {answer}
          </p>
        </div>
      )}
    </div>
  );
}

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="container mx-auto px-4 sm:px-6 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-md interactive">
              <span className="text-primary-foreground font-bold text-xl">
                A
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-xl sm:text-2xl font-bold text-foreground">
                AvencaLink
              </span>
              <span className="text-xs text-muted-foreground hidden sm:block">
                by Avenca Digital
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-4">
            <ThemeToggle />
            <Button
              variant="ghost"
              asChild
              animation="hover"
              className="hidden sm:flex rounded-2xl"
            >
              <Link href="/login">Entrar</Link>
            </Button>
            <Button
              asChild
              animation="lift"
              size="sm"
              className="sm:size-default rounded-2xl"
            >
              <Link href="/signup">Solicitar</Link>
            </Button>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center">
        <div className="max-w-5xl mx-auto space-y-6 sm:space-y-8">
          <div className="animate-fade-in">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
              Solução desenvolvida pela Avenca Digital
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-foreground mb-4 sm:mb-6 leading-tight px-4">
              Sua página de links
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                {" "}
                profissional
              </span>
            </h1>
          </div>

          <div className="animate-slide-up">
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground mb-8 sm:mb-10 max-w-3xl mx-auto leading-relaxed text-balance px-4">
              Desenvolvemos uma solução completa para você centralizar todos os
              seus links importantes. Design moderno, recursos avançados e
              totalmente personalizável.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 animate-slide-up px-4">
            <Button
              size="lg"
              asChild
              animation="lift"
              className="w-full sm:w-auto sm:min-w-[200px] text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-2xl h-14"
            >
              <Link href="/signup">Solicitar minha Página</Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              asChild
              animation="hover"
              className="w-full sm:w-auto sm:min-w-[200px] text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-2xl h-14"
            >
              <Link href="/demo">Ver Demonstração</Link>
            </Button>
          </div>

          {/* Demo Preview */}
          <div className="relative max-w-sm mx-auto animate-bounce-in px-4">
            <div className="bg-card rounded-3xl shadow-strong p-6 sm:p-8 border border-border/50 backdrop-blur-sm">
              <div className="text-center mb-6 sm:mb-8">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-primary to-primary/80 rounded-full mx-auto mb-4 sm:mb-6 shadow-lg interactive-lift"></div>
                <h3 className="font-semibold text-card-foreground text-base sm:text-lg mb-2">
                  @suaempresa
                </h3>
                <p className="text-xs sm:text-sm text-muted-foreground">
                  Especialista em soluções digitais
                </p>
              </div>

              <div className="space-y-3 sm:space-y-4">
                <div className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-xl p-3 sm:p-4 text-xs sm:text-sm transition-all duration-200 interactive cursor-pointer flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Compartilhe seus links
                </div>
                <div className="bg-muted/50 hover:bg-muted/80 rounded-xl p-3 sm:p-4 text-xs sm:text-sm transition-all duration-200 interactive cursor-pointer flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.**************.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.747 2.848c-.269 1.045-1.004 2.352-1.498 3.146 1.123.345 2.306.535 3.55.535 6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z" />
                  </svg>
                  Divulgue suas redes sociais
                </div>
                <div className="bg-muted/50 hover:bg-muted/80 rounded-xl p-3 sm:p-4 text-xs sm:text-sm transition-all duration-200 interactive cursor-pointer flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                  </svg>
                  Facilite seus contatos
                </div>
                <div className="bg-muted/50 hover:bg-muted/80 rounded-xl p-2 text-xs transition-all duration-200 interactive cursor-pointer">
                  <div className="flex items-center justify-between">
                    <span>📍 Divulgue sua Localização</span>
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
                <div className="bg-muted/50 hover:bg-muted/80 rounded-xl p-2 text-xs transition-all duration-200 interactive cursor-pointer">
                  <div className="flex items-center justify-between">
                    <span>⭐ Exiba suas Avaliações (4.9/5)</span>
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Features Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24">
        <div className="text-center mb-12 sm:mb-16 animate-fade-in">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 px-4">
            Recursos Avançados Inclusos
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4">
            Nossa solução oferece recursos profissionais para maximizar sua
            presença online
          </p>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 max-w-6xl mx-auto">
          <div className="text-center group animate-slide-up px-4">
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-primary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Links Inteligentes
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Sistema avançado de links com ícones personalizados, cores
              customizáveis e animações suaves
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.1s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-success/10 dark:bg-success/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-success/20 dark:group-hover:bg-success/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Galeria Profissional
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Galeria de imagens com lightbox, carrossel interativo e
              visualização em tela cheia
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.2s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-warning/10 dark:bg-warning/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-warning/20 dark:group-hover:bg-warning/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-warning"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Sistema de Avaliações
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Exiba depoimentos e avaliações dos clientes com carrossel
              responsivo e design elegante
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.3s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-blue-500/10 dark:bg-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-blue-500/20 dark:group-hover:bg-blue-500/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Localização Integrada
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Mapa interativo, informações de contato e horário de funcionamento
              em uma seção dedicada
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.4s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-purple-500/10 dark:bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-purple-500/20 dark:group-hover:bg-purple-500/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-purple-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Totalmente Personalizável
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Cores customizadas, temas personalizados e layout adaptável para
              sua marca
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.5s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-green-500/10 dark:bg-green-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-green-500/20 dark:group-hover:bg-green-500/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Performance Otimizada
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Construído com Next.js 15 e React 19 para máxima velocidade e SEO
              otimizado
            </p>
          </div>
        </div>
      </section>

      {/* Como Funciona Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-muted/20 to-muted/5">
        <div className="text-center mb-12 sm:mb-16 animate-fade-in">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 px-4">
            Nossa Metodologia
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4">
            Processo profissional da Avenca Digital para criar sua presença
            online
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 sm:gap-16 max-w-7xl mx-auto">
          {/* Esquerda - Processo */}
          <div className="space-y-8">
            <div className="flex items-start space-x-4 animate-slide-up">
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0">
                1
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl">
                  Análise e Planejamento
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Entendemos suas necessidades e objetivos para criar uma
                  estratégia personalizada que maximize sua presença online.
                </p>
              </div>
            </div>

            <div
              className="flex items-start space-x-4 animate-slide-up"
              style={{ animationDelay: "0.1s" }}
            >
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0">
                2
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl">
                  Design e Desenvolvimento
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Criamos sua página com design exclusivo, otimizada para
                  conversão e alinhada com sua identidade visual.
                </p>
              </div>
            </div>

            <div
              className="flex items-start space-x-4 animate-slide-up"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0">
                3
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl">
                  Configuração Avançada
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Implementamos recursos como galeria, avaliações, localização e
                  integração com suas redes sociais.
                </p>
              </div>
            </div>

            <div
              className="flex items-start space-x-4 animate-slide-up"
              style={{ animationDelay: "0.3s" }}
            >
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0">
                4
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl">
                  Entrega e Suporte
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Sua página é entregue otimizada, com domínio personalizado e
                  suporte contínuo da nossa equipe.
                </p>
              </div>
            </div>
          </div>

          {/* Direita - Recursos */}
          <div className="space-y-6 sm:space-y-8">
            <div className="bg-card rounded-2xl p-6 sm:p-8 border border-border/50 shadow-md animate-slide-up">
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-success/10 dark:bg-success/20 rounded-2xl flex items-center justify-center mb-4 sm:mb-6">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 text-success"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
                Hospedagem Gratuita
              </h3>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                Hospedagem profissional incluída com CDN global, SSL automático
                e 99.9% de uptime. Sem custos adicionais.
              </p>
            </div>

            <div
              className="bg-card rounded-2xl p-6 sm:p-8 border border-border/50 shadow-md animate-slide-up"
              style={{ animationDelay: "0.1s" }}
            >
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mb-4 sm:mb-6">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
                Domínio Personalizado
              </h3>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                Use seu próprio domínio ou escolha um subdomínio gratuito.
                Configuração automática de DNS e certificado SSL.
              </p>
            </div>

            {/* <div
              className="bg-card rounded-2xl p-6 sm:p-8 border border-border/50 shadow-md animate-slide-up"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-warning/10 dark:bg-warning/20 rounded-2xl flex items-center justify-center mb-4 sm:mb-6">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 text-warning"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
                Analytics Avançados
              </h3>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                Acompanhe cliques, visitantes e performance dos seus links com
                relatórios detalhados e insights em tempo real.
              </p>
            </div> */}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12 sm:mb-16 animate-fade-in">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6">
              Perguntas Frequentes
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Esclarecemos as principais dúvidas sobre nosso serviço
            </p>
          </div>

          <div className="space-y-4 sm:space-y-6">
            <FAQItem
              question="Por que escolher a Avenca Digital para minha página de links?"
              answer="Somos uma agência especializada em soluções digitais com anos de experiência. Desenvolvemos uma plataforma robusta, moderna e totalmente personalizada. Diferente de soluções genéricas, oferecemos suporte técnico especializado e recursos avançados como galeria profissional, sistema de avaliações e integração com mapas."
            />

            <FAQItem
              question="Qual a diferença do AvencaLink para outras soluções do mercado?"
              answer="Nossa solução é desenvolvida com tecnologias de ponta (Next.js 15, React 19) garantindo performance superior. Oferecemos recursos únicos como galeria com lightbox, sistema de avaliações com carrossel, mapa interativo e personalização completa de cores e temas. Além disso, você tem suporte direto da nossa equipe técnica."
            />

            <FAQItem
              question="Como funciona a personalização da página?"
              answer="Cada página é totalmente customizável. Você pode alterar cores, adicionar sua logo, escolher entre diferentes layouts, configurar galeria de imagens, adicionar sistema de avaliações, integrar localização com mapa e muito mais. Tudo isso mantendo a identidade visual da sua marca."
            />

            <FAQItem
              question="Que tipo de recursos avançados estão inclusos?"
              answer="Incluímos: sistema de links inteligentes com ícones personalizados, galeria profissional com visualização em tela cheia, carrossel de avaliações/depoimentos, mapa interativo com informações de contato, integração com redes sociais, otimização para SEO e analytics detalhados."
            />

            <FAQItem
              question="Como é o suporte técnico?"
              answer="Oferecemos suporte técnico especializado da nossa equipe. Desde a configuração inicial até atualizações futuras, você terá acesso direto aos desenvolvedores. Não é um chatbot ou FAQ genérico - é suporte humano e personalizado."
            />

            <FAQItem
              question="A página funciona bem em dispositivos móveis?"
              answer="Sim! Desenvolvemos com abordagem mobile-first. Sua página terá design responsivo perfeito, carregamento otimizado e experiência de usuário superior em smartphones, tablets e desktops. Testamos em diversos dispositivos para garantir compatibilidade total."
            />

            <FAQItem
              question="Posso usar meu próprio domínio?"
              answer="Claro! Você pode usar seu domínio personalizado (ex: links.suaempresa.com) ou utilizar nosso subdomínio gratuito. Configuramos tudo para você, incluindo certificado SSL e otimizações de performance."
            />

            <FAQItem
              question="Como funciona a hospedagem e manutenção?"
              answer="Cuidamos de toda a infraestrutura: hospedagem em servidores de alta performance, backups automáticos, atualizações de segurança, monitoramento 24/7 e otimizações contínuas. Você foca no seu negócio, nós cuidamos da tecnologia."
            />

            <FAQItem
              question="Posso migrar de outras plataformas de links?"
              answer="Sim! Ajudamos na migração completa dos seus links existentes. Nossa equipe faz a transição sem perda de dados e ainda melhora a apresentação com nossos recursos avançados. O processo é rápido e sem complicações."
            />

            <FAQItem
              question="Qual o investimento para ter minha página?"
              answer="Oferecemos diferentes planos para atender desde profissionais autônomos até grandes empresas. O investimento varia conforme os recursos necessários. Entre em contato para um orçamento personalizado - temos certeza que encontraremos a solução ideal para seu orçamento."
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl sm:rounded-3xl p-8 sm:p-10 lg:p-12 shadow-strong">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-primary-foreground mb-4 sm:mb-6 px-4">
              Pronto para ter sua página profissional?
            </h2>
            <p className="text-base sm:text-lg text-primary-foreground/90 mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed px-4">
              Entre em contato com a Avenca Digital e tenha uma página de links
              exclusiva, moderna e otimizada para conversão.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
              <Button
                size="lg"
                asChild
                variant="secondary"
                className="w-full sm:w-auto sm:min-w-[240px] text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 bg-white hover:bg-white/90 text-primary rounded-2xl h-14"
              >
                <Link href="/signup">Solicitar Orçamento</Link>
              </Button>
              <Button
                size="lg"
                asChild
                variant="outline"
                className="w-full sm:w-auto sm:min-w-[240px] text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 bg-white hover:bg-white/90 text-primary rounded-2xl h-14"
              >
                <Link href="/demo">Ver Demonstração</Link>
              </Button>
            </div>

            <div className="mt-8 pt-6 border-t border-primary-foreground/20">
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-primary-foreground/80 text-sm">
                <div className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Hospedagem e SSL inclusos
                </div>
                <div className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Personalização completa
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/50 py-8 sm:py-12 mt-16 sm:mt-20 lg:mt-24">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <div className="flex items-center justify-center space-x-2 sm:space-x-3 mb-4 sm:mb-6">
            <div className="w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-sm">
              <span className="text-primary-foreground font-bold text-sm">
                A
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-base sm:text-lg font-semibold text-foreground">
                AvencaLink
              </span>
              <span className="text-xs text-muted-foreground">
                by Avenca Digital
              </span>
            </div>
          </div>
          <p className="text-muted-foreground text-xs sm:text-sm max-w-md mx-auto leading-relaxed px-4">
            © 2025 Avenca Digital. Todos os direitos reservados.
            <br />
            Soluções digitais profissionais para sua presença online.
          </p>
        </div>
      </footer>
    </div>
  );
}
