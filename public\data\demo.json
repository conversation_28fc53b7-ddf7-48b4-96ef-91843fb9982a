{"user": {"username": "demo", "name": "Demo User", "bio": "This is a demo profile showcasing API-based button configuration with custom colors", "avatar": "https://db.avenca.cloud/images/2025/01/20/user_placeholder.png"}, "links": [{"text": "My Website", "url": "https://example.com", "classIcon": "fa fa-globe"}, {"text": "Contact Me", "url": "mailto:<EMAIL>", "classIcon": "fa fa-envelope"}], "socialMedia": [{"text": "Instagram", "url": "https://instagram.com/demo", "classIcon": "fa fa-instagram"}, {"text": "WhatsApp", "url": "https://wa.me/5511999999999", "classIcon": "fa fa-whatsapp"}], "phone": "+55 11 99999-9999", "settings": {"colors": {"background": "#0f172a", "linkText": "#f1f5f9", "primary": "#3b82f6", "secondary": "#64748b", "socialIconBackground": "#1e293b"}, "favicon": "", "pageDescription": "Demo profile with custom button configuration and colors", "pageKeywords": "demo, profile, buttons, api, colors"}, "featuresSection": {"title": "Amazing Features", "description": "Check out these features with custom blue buttons from API", "enabled": true, "buttonConfig": {"primaryButtonText": "View Feature", "secondaryButtonText": "More Info", "showBadge": true}, "items": [{"id": 1, "title": "Feature with Custom Buttons", "description": "This feature uses custom button text and blue colors from API configuration", "image": "https://images.unsplash.com/photo-1660505102581-85cffa4e6550", "primaryButton": {"icon": "fa fa-eye", "url": "https://example.com/feature1"}, "secondaryButton": {"icon": "fa fa-info-circle", "url": "https://example.com/feature1-info"}}, {"id": 2, "title": "Another Feature", "description": "Second feature to test carousel navigation with API colors", "image": "https://images.unsplash.com/photo-1551434678-e076c223a692", "primaryButton": {"icon": "fa fa-play", "url": "https://example.com/feature2"}, "secondaryButton": {"icon": "fa fa-download", "url": "https://example.com/feature2-download"}}]}, "servicesSection": {"title": "Professional Services", "description": "Services with custom green buttons from API", "enabled": true, "buttonConfig": {"primaryButtonText": "Book Now", "secondaryButtonText": "Get Quote", "showBadge": false}, "items": [{"id": 1, "title": "Service with Custom Buttons", "description": "This service uses custom button text and colors from API configuration", "image": "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d", "primaryButton": {"icon": "fa fa-calendar", "url": "https://example.com/book-service1"}, "secondaryButton": {"icon": "fa fa-phone", "url": "https://wa.me/5511999999999"}}]}, "genericSection": {"title": "Additional Content", "description": "Generic content using default button configuration with API colors", "enabled": true, "items": [{"id": 1, "title": "Generic Content Item", "description": "This item uses default button text but API colors should still apply", "image": "https://images.unsplash.com/photo-1542744173-8e7e53415bb0", "primaryButton": {"icon": "fa fa-eye", "url": "https://example.com/generic1"}, "secondaryButton": {"icon": "fa fa-phone", "url": "https://wa.me/5511999999999"}}]}, "gallery": {"title": "Photo Gallery", "description": "My photo collection", "enabled": false, "images": []}, "reviews": {"title": "Customer Reviews", "description": "What people say about me", "enabled": false, "reviews": []}, "video": {"title": "Featured Video", "description": "Watch my latest video", "enabled": false, "youtubeUrl": ""}}