import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import useEmblaCarousel from "embla-carousel-react";
import { useCallback, useEffect, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { createColorVariations, isValidCSSColor } from "@/lib/colorUtils";

interface Review {
  id: number;
  name: string;
  photo: string;
  comment: string;
  rating: number;
}

interface ReviewsProps {
  reviews: {
    title: string;
    description: string;
    enabled: boolean;
    reviews: Review[];
  };
  colors?: {
    background?: string;
    linkText?: string;
    primary?: string;
    secondary?: string;
    socialIconBackground?: string;
  };
}

const Reviews = ({ reviews, colors }: ReviewsProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: true,
    loop: false,
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);
  }, [emblaApi, onSelect]);

  if (!reviews.enabled) return null;

  return (
    <section className="mb-12 animate-fade-in">
      <div className="text-center mb-8 lg:mb-12">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3">
          {reviews.title}
        </h2>
        <p className="text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed">
          {reviews.description}
        </p>
      </div>

      <div className="relative">
        <div className="overflow-hidden rounded-xl" ref={emblaRef}>
          <div className="flex gap-4">
            {reviews.reviews.map((review, index) => (
              <div
                key={review.id}
                className="flex-[0_0_90%] sm:flex-[0_0_85%] md:flex-[0_0_80%] lg:flex-[0_0_360px] min-w-0"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <Card className="shadow-soft hover:shadow-strong transition-all duration-500 hover:scale-[1.02] transform-gpu group h-full">
                  <CardContent className="p-4 sm:p-6 h-full flex flex-col">
                    <div className="flex items-start space-x-3 sm:space-x-4 mb-4">
                      <Avatar
                        className="w-10 h-10 sm:w-12 sm:h-12 shrink-0 ring-2 transition-all duration-300"
                        style={
                          colors?.primary && isValidCSSColor(colors.primary)
                            ? (() => {
                                const colorVariations = createColorVariations(
                                  colors.primary
                                );
                                return {
                                  "--ring-color": `${colorVariations.light}`,
                                  "--ring-hover-color": `${colorVariations.base}33`,
                                } as React.CSSProperties;
                              })()
                            : {}
                        }
                      >
                        <AvatarImage
                          src={review.photo}
                          alt={review.name}
                          className="object-cover"
                        />
                        <AvatarFallback
                          className="font-bold text-sm sm:text-base"
                          style={
                            colors?.primary && isValidCSSColor(colors.primary)
                              ? {
                                  backgroundColor: colors.primary,
                                  color: colors.linkText || "#ffffff",
                                }
                              : {}
                          }
                        >
                          {review.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2 mb-2">
                          <h4 className="font-semibold text-foreground text-sm sm:text-base truncate">
                            {review.name}
                          </h4>
                          <div
                            className="flex text-yellow-400 shrink-0"
                            aria-label={`${review.rating} estrelas`}
                          >
                            {[...Array(review.rating)].map((_, i) => (
                              <i
                                key={i}
                                className="fas fa-star text-xs sm:text-sm group-hover:scale-110 transition-transform duration-300"
                                style={{ animationDelay: `${i * 0.1}s` }}
                                aria-hidden="true"
                              ></i>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Review comment with better typography */}
                    <blockquote className="flex-1 flex items-center">
                      <p className="text-muted-foreground text-xs sm:text-sm leading-relaxed italic">
                        &ldquo;{review.comment}&rdquo;
                      </p>
                    </blockquote>

                    {/* Decorative quote mark */}
                    <div className="mt-4 flex justify-end opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                      <i
                        className="fas fa-quote-right text-2xl"
                        style={{
                          color:
                            colors?.primary && isValidCSSColor(colors.primary)
                              ? colors.primary
                              : undefined,
                        }}
                      ></i>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-center mt-8 gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={scrollPrev}
            disabled={!canScrollPrev}
            className="w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform"
            style={
              colors?.primary && isValidCSSColor(colors.primary)
                ? (() => {
                    const colorVariations = createColorVariations(
                      colors.primary
                    );
                    return {
                      backgroundColor: `${colorVariations.base}CC`, // 80% opacity
                      color: colors.linkText || "#ffffff",
                      borderColor: "transparent",
                    } as React.CSSProperties;
                  })()
                : {}
            }
            onMouseEnter={(e) => {
              if (
                colors?.primary &&
                isValidCSSColor(colors.primary) &&
                !e.currentTarget.disabled
              ) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = colorVariations.base;
              }
            }}
            onMouseLeave={(e) => {
              if (
                colors?.primary &&
                isValidCSSColor(colors.primary) &&
                !e.currentTarget.disabled
              ) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = `${colorVariations.base}CC`;
              }
            }}
            aria-label="Avaliação anterior"
          >
            <ChevronLeft className="w-5 h-5" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={scrollNext}
            disabled={!canScrollNext}
            className="w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform"
            style={
              colors?.primary && isValidCSSColor(colors.primary)
                ? (() => {
                    const colorVariations = createColorVariations(
                      colors.primary
                    );
                    return {
                      backgroundColor: `${colorVariations.base}CC`, // 80% opacity
                      color: colors.linkText || "#ffffff",
                      borderColor: "transparent",
                    } as React.CSSProperties;
                  })()
                : {}
            }
            onMouseEnter={(e) => {
              if (
                colors?.primary &&
                isValidCSSColor(colors.primary) &&
                !e.currentTarget.disabled
              ) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = colorVariations.base;
              }
            }}
            onMouseLeave={(e) => {
              if (
                colors?.primary &&
                isValidCSSColor(colors.primary) &&
                !e.currentTarget.disabled
              ) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = `${colorVariations.base}CC`;
              }
            }}
            aria-label="Próxima avaliação"
          >
            <ChevronRight className="w-5 h-5" />
          </Button>
        </div>

        {/* Progress indicator */}
        <div className="flex justify-center mt-4 gap-2">
          {reviews.reviews.map((_, index) => (
            <div
              key={index}
              className="w-2 h-2 rounded-full transition-all duration-300 cursor-pointer"
              style={{
                backgroundColor:
                  colors?.primary && isValidCSSColor(colors.primary)
                    ? `${colors.primary}66` // 40% opacity
                    : undefined,
              }}
              onMouseEnter={(e) => {
                if (colors?.primary && isValidCSSColor(colors.primary)) {
                  e.currentTarget.style.backgroundColor = colors.primary;
                }
              }}
              onMouseLeave={(e) => {
                if (colors?.primary && isValidCSSColor(colors.primary)) {
                  e.currentTarget.style.backgroundColor = `${colors.primary}66`;
                }
              }}
              onClick={() => emblaApi?.scrollTo(index)}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Reviews;
