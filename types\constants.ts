// ============================================================================
// CONSTANTS AND ENUMS
// ============================================================================

/**
 * Common icon classes used throughout the application
 */
export const ICON_CLASSES = {
  // Social Media
  WHATSAPP: 'fa fa-whatsapp',
  INSTAGRAM: 'fa fa-instagram',
  FACEBOOK: 'fa fa-facebook',
  YOUTUBE: 'fa fa-youtube',
  TIKTOK: 'fa fa-video',
  PINTEREST: 'fa fa-pinterest',
  TWITTER: 'fa fa-twitter',
  LINKEDIN: 'fa fa-linkedin',

  // Actions
  PHONE: 'fa fa-phone',
  EMAIL: 'fa fa-envelope',
  WEBSITE: 'fa fa-globe',
  LOCATION: 'fa fa-map-marker',

  // Content
  BOOK: 'fa fa-book',
  FILE: 'fa fa-file-text',
  FOLDER: 'fa fa-folder-open',
  FORM: 'fa fa-wpforms',
  EYE: 'fa fa-eye',
  INFO: 'fa fa-info-circle',
  USERS: 'fa fa-users',

  // Placeholder
  PLACEHOLDER: '#',
} as const

/**
 * Default color schemes
 */
export const DEFAULT_COLOR_SCHEMES = {
  DARK: {
    background: '#1f1f1f',
    linkText: '#ffffff',
    primary: '#393939',
    secondary: '#828282',
    socialIconBackground: '#393939',
  },
  LIGHT: {
    background: '#d7d6d6',
    linkText: '#ffffff',
    primary: '#393939',
    secondary: '#828282',
    socialIconBackground: '#393939',
  },
  WARM: {
    background: '#d7d5d3',
    linkText: '#ffffff',
    primary: '#201f1f',
    secondary: '#c7b29d',
    socialIconBackground: '#201f1f',
  },
  NEUTRAL: {
    background: '#d8d8d8',
    linkText: '#d8d8d8',
    primary: '#1f1f1f',
    secondary: '#727272',
    socialIconBackground: '#979797',
  },
} as const

/**
 * Rating values for reviews
 */
export const RATING_VALUES = [1, 2, 3, 4, 5] as const

/**
 * Section types
 */
export const SECTION_TYPES = {
  FEATURES: 'featuresSection',
  SERVICES: 'servicesSection',
  GENERIC: 'genericSection',
  GALLERY: 'gallery',
  REVIEWS: 'reviews',
  VIDEO: 'video',
} as const

/**
 * Button types
 */
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
} as const

/**
 * Default button configurations for different section types
 */
export const SECTION_BUTTON_CONFIG = {
  features: {
    primaryButtonText: 'Ver',
    secondaryButtonText: 'Contato',
    showBadge: false,
  },
  services: {
    primaryButtonText: 'Solicitar',
    secondaryButtonText: 'Info',
    showBadge: true,
  },
  generic: {
    primaryButtonText: 'Ver Mais',
    secondaryButtonText: 'Contato',
    showBadge: false,
  },
} as const

/**
 * Common URL patterns for validation
 */
export const URL_PATTERNS = {
  WHATSAPP: /^https:\/\/wa\.me\//,
  INSTAGRAM: /^https:\/\/(www\.)?instagram\.com\//,
  FACEBOOK: /^https:\/\/(www\.)?facebook\.com\//,
  YOUTUBE: /^https:\/\/(www\.)?youtube\.com\//,
  TIKTOK: /^https:\/\/(www\.)?tiktok\.com\//,
  PINTEREST: /^https:\/\/(www\.)?pinterest\.com\//,
  GOOGLE_DRIVE: /^https:\/\/drive\.google\.com\//,
  GOOGLE_DOCS: /^https:\/\/docs\.google\.com\//,
  GOOGLE_FORMS: /^https:\/\/forms\.gle\//,
  GOOGLE_MAPS: /^https:\/\/maps\.app\.goo\.gl\//,
} as const

/**
 * Image placeholder URLs
 */
export const PLACEHOLDER_IMAGES = {
  USER_AVATAR: 'https://db.avenca.cloud/images/2025/01/20/user_placeholder.png',
  GALLERY_IMAGE: 'https://images.unsplash.com/photo-1660505102581-85cffa4e6550',
  RANDOM_USER_FEMALE: 'https://randomuser.me/api/portraits/women/',
  RANDOM_USER_MALE: 'https://randomuser.me/api/portraits/men/',
} as const

/**
 * Default values for new sections
 */
export const DEFAULT_SECTION_VALUES = {
  ENABLED: true,
  DISABLED: false,
  EMPTY_DESCRIPTION: '',
  EMPTY_TITLE: '',
  EMPTY_URL: '#',
  PLACEHOLDER_ICON: '#',
} as const

/**
 * Type definitions for constants
 */
export type IconClass = typeof ICON_CLASSES[keyof typeof ICON_CLASSES]
export type ColorScheme = typeof DEFAULT_COLOR_SCHEMES[keyof typeof DEFAULT_COLOR_SCHEMES]
export type RatingValue = typeof RATING_VALUES[number]
export type SectionType = typeof SECTION_TYPES[keyof typeof SECTION_TYPES]
export type ButtonType = typeof BUTTON_TYPES[keyof typeof BUTTON_TYPES]
export type SectionButtonConfig = typeof SECTION_BUTTON_CONFIG[keyof typeof SECTION_BUTTON_CONFIG]
