import React from "react";
import { UserProfile } from "@/types/user";
import { transformLocationData } from "@/lib/brokenInkUtils";

interface LocationSectionProps {
  profile: UserProfile;
}

const LocationSection = ({ profile }: LocationSectionProps) => {
  const locationData = transformLocationData(profile);

  if (!locationData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24" id="location">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {locationData.title}
          </h2>
          {locationData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {locationData.description}
            </p>
          )}
          {locationData.address && (
            <p className="text-gray-400 text-base mt-2">
              {locationData.address}
            </p>
          )}
        </div>

        {/* Hours Information */}
        {locationData.hours && (
          <div className="space-y-2">
            <h3 className="text-white text-xl font-semibold mb-4 flex justify-center">
              Horário de Funcionamento
            </h3>
            {locationData.hours.weekdays && (
              <div className="flex justify-center gap-4">
                <span className="text-gray-400">Segunda a Sexta:</span>
                <span className="text-gray-300">
                  {locationData.hours.weekdays}
                </span>
              </div>
            )}
            {locationData.hours.weekends && (
              <div className="flex justify-center gap-4">
                <span className="text-gray-400">Sábado:</span>
                <span className="text-gray-300">
                  {locationData.hours.weekends}
                </span>
              </div>
            )}
            {locationData.hours.closed && (
              <div className="flex justify-center gap-4">
                <span className="text-gray-400">
                  {locationData.hours.closed}:
                </span>
                <span className="text-gray-300">Fechado</span>
              </div>
            )}
          </div>
        )}

        {/* Google Maps Link */}
        {locationData.googleMapsUrl && (
          <div className="text-center mt-6">
            <a
              href={locationData.googleMapsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-6 py-3  rounded-lg  transition-colors"
            >
              <i className="fa fa-external-link"></i>
              Abrir no Google Maps
            </a>
          </div>
        )}
      </div>
    </section>
  );
};

export default LocationSection;
