import React from "react";
import { UserProfile } from "@/types/user";
import { transformLocationData } from "@/lib/brokenInkUtils";

interface LocationSectionProps {
  profile: UserProfile;
}

const LocationSection = ({ profile }: LocationSectionProps) => {
  const locationData = transformLocationData(profile);

  if (!locationData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24" id="location">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {locationData.title}
          </h2>
          {locationData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {locationData.description}
            </p>
          )}
          {locationData.address && (
            <p className="text-gray-400 text-base mt-2">
              {locationData.address}
            </p>
          )}
        </div>
        <div className="aspect-video w-full rounded-2xl overflow-hidden shadow-lg">
          {/* Placeholder for map integration.
              For example, Google Maps iframe or a React map library.
              A simple placeholder text or image could also go here.
          */}
          <div className="bg-gray-700 w-full h-full flex items-center justify-center text-white">
            {locationData.coordinates
              ? `Lat: ${locationData.coordinates.lat}, Lng: ${locationData.coordinates.lng}`
              : "[Map Placeholder]"}
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationSection;
